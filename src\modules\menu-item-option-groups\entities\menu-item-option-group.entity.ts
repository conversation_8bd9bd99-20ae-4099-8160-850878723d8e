import { Exclude, Expose } from 'class-transformer';
import { isNil } from 'lodash';
import { Column, Entity, Generated, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { MappingMenuItemMenuItemOptionGroup } from '@/modules/menu-items/entities/mapping-menu-item-menu-item-option-group.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

import { MappingMenuItemOptionGroupMenuItemOption } from './mapping-menu-item-option-group-menu-item-option.entity';

export enum MenuItemOptionGroupRule {
  OPTION = 'option',
  OPTION_MAX = 'option_max',
  MANDATORY_FIXED = 'mandatory_fixed',
  MANDATORY_RANGE = 'mandatory_range',
}

export enum MenuItemOptionGroupType {
  ITEM_CUSTOMIZATION = 'item_customization',
  ADD_ON_ITEM = 'add_on_item',
}

@Entity('menu_item_option_groups')
@Index(['code', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL' })
@Index(['internalName', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL' })
@Index(['publishedName', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL' })
export class MenuItemOptionGroup extends BaseEntity {
  @Generated('uuid')
  @Index({ unique: true })
  @Column({ name: 'code', type: 'uuid' })
  code: string;

  @Column({ name: 'internal_name', type: 'varchar' })
  internalName: string;

  @Column({ name: 'published_name', type: 'varchar' })
  publishedName: string;

  @Column({ name: 'published_name_en', type: 'varchar', nullable: true })
  publishedNameEn?: string | null;

  @Column({ name: 'published_name_vi', type: 'varchar', nullable: true })
  publishedNameVi?: string | null;

  @Column({ type: 'enum', enum: MenuItemOptionGroupRule, default: MenuItemOptionGroupRule.OPTION })
  rule: MenuItemOptionGroupRule;

  @Column({ type: 'enum', enum: MenuItemOptionGroupType, default: MenuItemOptionGroupType.ITEM_CUSTOMIZATION })
  type: MenuItemOptionGroupType;

  @Column({ name: 'max_amount_of_option', type: 'int', default: 1 })
  maxAmountOfOption: number;

  @Column({ name: 'max_amount', nullable: true, type: 'int' })
  maxAmount?: number | null;

  @Column({ name: 'fixed_amount', nullable: true, type: 'int' })
  fixedAmount?: number | null;

  @Column({ name: 'from_amount', nullable: true, type: 'int' })
  fromAmount?: number | null;

  @Column({ name: 'to_amount', nullable: true, type: 'int' })
  toAmount?: number | null;

  @Index()
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @ManyToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant: WrapperType<Restaurant>;

  @Expose()
  get menuItems() {
    return this.mappingMenuItemMenus?.map((m) => m.menuItem).filter((m) => !isNil(m));
  }

  @Exclude()
  @OneToMany(() => MappingMenuItemMenuItemOptionGroup, (mappingMenuItemMenu) => mappingMenuItemMenu.menuItemOptionGroup)
  mappingMenuItemMenus?: WrapperType<MappingMenuItemMenuItemOptionGroup>[];

  @Expose()
  get menuItemOptions() {
    return this.mappingMenuItemOptions
      ?.filter((m) => !isNil(m.menuItemOption))
      .map((m) => {
        m.menuItemOption.position = m.position;
        // Use mapping price if available, otherwise use basePrice
        m.menuItemOption.price = m.price;
        return m.menuItemOption;
      })
      .sort((a, b) => a.position - b.position);
  }

  @Exclude()
  @OneToMany(
    () => MappingMenuItemOptionGroupMenuItemOption,
    (mappingMenuItemOption) => mappingMenuItemOption.menuItemOptionGroup,
  )
  mappingMenuItemOptions?: WrapperType<MappingMenuItemOptionGroupMenuItemOption>[];

  // field not in database
  position: number;
  optionCount: number;
  itemCount: number;
  optionPrice?: number | null;
}
