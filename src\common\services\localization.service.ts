import { Injectable } from '@nestjs/common';

import { Language } from '@/modules/users/entities/user.entity';

export interface LocalizableEntity {
  publishedName: string;
  publishedNameEn?: string | null;
  publishedNameVi?: string | null;
}

@Injectable()
export class LocalizationService {
  /**
   * Get localized name based on user's language preference
   * @param entity - Entity with localized name fields
   * @param userLanguage - User's preferred language
   * @returns Localized name or fallback to publishedName
   */
  getLocalizedName(entity: LocalizableEntity, userLanguage?: Language): string {
    if (!userLanguage) {
      return entity.publishedName;
    }

    switch (userLanguage) {
      case Language.VI:
        return entity.publishedNameVi || entity.publishedName;
      case Language.EN:
        return entity.publishedNameEn || entity.publishedName;
      default:
        return entity.publishedName;
    }
  }

  /**
   * Apply localization to a single entity
   * @param entity - Entity to localize
   * @param userLanguage - User's preferred language
   * @returns Entity with localized publishedName
   */
  localizeEntity<T extends LocalizableEntity>(entity: T, userLanguage?: Language): T {
    if (!entity) return entity;

    const localizedName = this.getLocalizedName(entity, userLanguage);
    
    return {
      ...entity,
      publishedName: localizedName,
    };
  }

  /**
   * Apply localization to an array of entities
   * @param entities - Array of entities to localize
   * @param userLanguage - User's preferred language
   * @returns Array of entities with localized publishedName
   */
  localizeEntities<T extends LocalizableEntity>(entities: T[], userLanguage?: Language): T[] {
    if (!entities || entities.length === 0) return entities;

    return entities.map(entity => this.localizeEntity(entity, userLanguage));
  }

  /**
   * Apply localization to nested entities within a parent entity
   * @param parentEntity - Parent entity containing nested localizable entities
   * @param nestedEntityKey - Key of the nested entity/entities
   * @param userLanguage - User's preferred language
   * @returns Parent entity with localized nested entities
   */
  localizeNestedEntities<T, K extends keyof T>(
    parentEntity: T,
    nestedEntityKey: K,
    userLanguage?: Language,
  ): T {
    if (!parentEntity || !parentEntity[nestedEntityKey]) {
      return parentEntity;
    }

    const nestedValue = parentEntity[nestedEntityKey];

    if (Array.isArray(nestedValue)) {
      return {
        ...parentEntity,
        [nestedEntityKey]: this.localizeEntities(nestedValue as LocalizableEntity[], userLanguage),
      };
    } else if (typeof nestedValue === 'object' && nestedValue !== null) {
      return {
        ...parentEntity,
        [nestedEntityKey]: this.localizeEntity(nestedValue as LocalizableEntity, userLanguage),
      };
    }

    return parentEntity;
  }
}
