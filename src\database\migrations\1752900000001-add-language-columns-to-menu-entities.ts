import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLanguageColumnsToMenuEntities1752900000001 implements MigrationInterface {
  name = 'AddLanguageColumnsToMenuEntities1752900000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add language columns to menu_sections table
    await queryRunner.query(`ALTER TABLE "menu_sections" ADD "published_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_sections" ADD "published_name_vi" character varying`);
    
    // Copy current published_name to published_name_en as default
    await queryRunner.query(`UPDATE "menu_sections" SET "published_name_en" = "published_name"`);
    
    // Add language columns to menu_items table
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "published_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_items" ADD "published_name_vi" character varying`);
    
    // Copy current published_name to published_name_en as default
    await queryRunner.query(`UPDATE "menu_items" SET "published_name_en" = "published_name"`);
    
    // Add language columns to menu_item_option_groups table
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" ADD "published_name_en" character varying`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" ADD "published_name_vi" character varying`);
    
    // Copy current published_name to published_name_en as default
    await queryRunner.query(`UPDATE "menu_item_option_groups" SET "published_name_en" = "published_name"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove language columns from menu_item_option_groups table
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP COLUMN "published_name_vi"`);
    await queryRunner.query(`ALTER TABLE "menu_item_option_groups" DROP COLUMN "published_name_en"`);
    
    // Remove language columns from menu_items table
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "published_name_vi"`);
    await queryRunner.query(`ALTER TABLE "menu_items" DROP COLUMN "published_name_en"`);
    
    // Remove language columns from menu_sections table
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP COLUMN "published_name_vi"`);
    await queryRunner.query(`ALTER TABLE "menu_sections" DROP COLUMN "published_name_en"`);
  }
}
