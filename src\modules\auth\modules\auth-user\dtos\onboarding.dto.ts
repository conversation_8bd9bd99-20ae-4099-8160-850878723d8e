import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Empty, <PERSON><PERSON><PERSON><PERSON>, IsString, Length, Matches } from 'class-validator';

import { ToLowerCase } from '@/common/decorators/transforms.decorator';
import { Language } from '@/modules/users/entities/user.entity';
import { ApiProperty } from '@nestjs/swagger';

export class SendPhoneOtpDto {
  @ApiProperty({ example: '987654321', description: 'Phone number without country code' })
  @Matches(/^[0-9]{9,10}$/, { message: 'Phone number must be 9-10 digits' })
  @IsNotEmpty()
  phone: string;

  @ApiProperty({ example: '+84' })
  @IsString()
  @IsNotEmpty()
  phoneCountryCode: string;

  @ApiProperty({ example: 'en', description: 'User preferred language', enum: Language, required: false })
  @IsOptional()
  @IsEnum(Language)
  language?: Language;
}

export class VerifyPhoneOtpDto {
  @ApiProperty({ example: '987654321', description: 'Phone number without country code' })
  @Matches(/^[0-9]{9,10}$/, { message: 'Phone number must be 9-10 digits' })
  @IsNotEmpty()
  phone: string;

  @ApiProperty({ example: '123456' })
  @IsString()
  @Length(6, 6, { message: 'OTP must be 6 digits' })
  otp: string;

  @ApiProperty({ example: '+84' })
  @IsString()
  @IsNotEmpty()
  phoneCountryCode: string;

  @ApiProperty({ example: 'en', description: 'User preferred language', enum: Language, required: false })
  @IsOptional()
  @IsEnum(Language)
  language?: Language;
}

export class SendEmailOtpDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty()
  @ToLowerCase()
  email: string;

  @ApiProperty({ example: 'verification_token_from_phone_step' })
  @IsString()
  @IsNotEmpty()
  phoneVerificationToken: string;
}

export class VerifyEmailOtpDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty()
  @ToLowerCase()
  email: string;

  @ApiProperty({ example: '123456' })
  @IsString()
  @Length(6, 6, { message: 'OTP must be 6 digits' })
  otp: string;

  @ApiProperty({ example: 'verification_token_from_phone_step' })
  @IsString()
  @IsNotEmpty()
  phoneVerificationToken: string;
}

export class UpdateUserNameDto {
  @ApiProperty({ example: 'John' })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ example: 'Doe' })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({ example: 'en', description: 'User preferred language', enum: Language, required: false })
  @IsOptional()
  @IsEnum(Language)
  language?: Language;
}

export class FakeLoginDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty()
  email: string;
}
