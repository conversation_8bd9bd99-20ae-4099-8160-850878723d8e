import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLanguageToUser1752900000000 implements MigrationInterface {
  name = 'AddLanguageToUser1752900000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum type for language
    await queryRunner.query(`CREATE TYPE "public"."users_language_enum" AS ENUM('en', 'vi')`);
    
    // Add language column to users table with default value 'en'
    await queryRunner.query(`ALTER TABLE "users" ADD "language" "public"."users_language_enum" NOT NULL DEFAULT 'en'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove language column
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "language"`);
    
    // Drop enum type
    await queryRunner.query(`DROP TYPE "public"."users_language_enum"`);
  }
}
